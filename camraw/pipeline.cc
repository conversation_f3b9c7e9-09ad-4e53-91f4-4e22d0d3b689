#include <algorithm>
#include <cmath>
#include <list>
#include <thread>
#include <unordered_set>

#include "absl/status/status.h"
#include "glog/logging.h"

#include "camraw/imagemeta.h"
#include "camraw/pipeline.h"
#include "camraw/pipeline_accumulator.h"
#include "camraw/pipeline_element.h"
#include "camraw/pipeline_processor.h"
#include "camraw/pipeline_sink.h"
#include "camraw/pipeline_source.h"
#include "camraw/pipeline_task.h"
#include "camraw/pipeline_task_runner.h"
#include "camraw/status_macros.h"

namespace cmrw {

namespace {
void
PrintElementInfo(PipelineElement* e) {
   LOG(INFO) << "Running pipeline " << PipelineElementTypeToString(e->type())
             << ": " << e->name();
   VLOG(3) << "Input spec: " << "{ 1spp: "
           << e->input_spec().one_sample_per_pixel << ","
           << " 3spp: " << e->input_spec().three_samples_per_pixel << "}";
   VLOG(3) << "Output spec: " << "{ 1spp: "
           << e->output_spec().one_sample_per_pixel << ","
           << " 3spp: " << e->output_spec().three_samples_per_pixel << "}";
}
}  // namespace

absl::Status
Pipeline::AddSource(std::unique_ptr<PipelineSource> source) {
   if (elements_.size() > 0) {
      return absl::FailedPreconditionError(
          "Pipeline source can only be added as the first element");
   }

   last_output_spec_ = source->output_spec();

   elements_.push_back(std::move(source));

   return absl::OkStatus();
}

absl::Status
Pipeline::ValidateNewElement(const PipelineElement& element) {
   if (element.input_spec().one_sample_per_pixel &&
       element.input_spec().three_samples_per_pixel) {
      return absl::OkStatus();
   } else if (element.input_spec().one_sample_per_pixel) {
      if (!last_output_spec_.one_sample_per_pixel) {
         return absl::FailedPreconditionError(
             "1spp element being chained to an element that doesn't output "
             "1spp");
      }
   } else if (element.input_spec().three_samples_per_pixel) {
      if (!last_output_spec_.three_samples_per_pixel) {
         return absl::FailedPreconditionError(
             "3spp element being chained to an element that doesn't output "
             "3spp");
      }
   } else {
      return absl::FailedPreconditionError(
          "Element doesn't support any input sample sizes");
   }

   return absl::OkStatus();
}

absl::Status
Pipeline::AddProcessor(std::unique_ptr<PipelineProcessor> processor) {
   RETURN_IF_ERROR(ValidateNewElement(*processor));

   last_output_spec_ = processor->output_spec();

   elements_.push_back(std::move(processor));

   return absl::OkStatus();
}

absl::Status
Pipeline::AddSink(std::unique_ptr<PipelineSink> sink) {
   RETURN_IF_ERROR(ValidateNewElement(*sink));

   elements_.push_back(std::move(sink));

   return absl::OkStatus();
}

absl::Status
Pipeline::Run(unsigned int num_threads) {
   std::unique_ptr<ImageBuf<uint16_t>> prev_output = nullptr;
   std::unique_ptr<PipelineTaskRunner> task_runner = nullptr;
   std::vector<std::unique_ptr<PipelineAccumulator>> accumulators;
   // Number of components of the previous stage
   size_t prev_components = 1;
   // Pointer to the previous stage's element
   PipelineElement* prev_element = nullptr;
   // Pointer to the previous source
   PipelineSource* prev_source = nullptr;
   // Pointer to the previous processor
   PipelineProcessor* prev_processor = nullptr;
   // Image metadata
   std::unique_ptr<ImageMeta> image_meta = nullptr;
   // Image dimensions obtained from the pipeline source
   uint16_t image_width = 0, image_height = 0;
   // Samples per pixel from the pipeline source
   uint16_t samples_per_pixel;
   // Current pipeline stage ID (monotonically increasing from 0)
   int cur_stage_id = 0;

   if (num_threads == 0) {
      // Use max threads - 1 worker threads to keep the main thread uncontended
      num_threads = std::thread::hardware_concurrency() - 1;
      if (num_threads == 0) {
         num_threads = 1;
      }
   }

   if (num_threads > 1) {
      task_runner = std::make_unique<PipelineTaskRunner>(num_threads);
   }

   // Build set of required image metadata
   std::unordered_set<ImageMetaId> required_metadata = {
       // these metadata items are required by the pipeline framework
       ImageMetaId::IMAGE_WIDTH,
       ImageMetaId::IMAGE_HEIGHT,
       ImageMetaId::SAMPLES_PER_PIXEL,
   };
   std::unordered_set<ImageMetaId> optional_metadata;
   for (auto& e : elements_) {
      // these metadata items are required by individual pipeline elements
      const std::unordered_set<ImageMetaId>& element_requires =
          e->required_metadata();
      required_metadata.insert(element_requires.begin(),
                               element_requires.end());
      const std::unordered_set<ImageMetaId>& element_optional =
          e->optional_metadata();
      optional_metadata.insert(element_optional.begin(),
                               element_optional.end());
   }

   for (auto& e : elements_) {
      if (e->type() == PipelineElementType::SOURCE) {
         PrintElementInfo(e.get());
         PipelineSource* source = static_cast<PipelineSource*>(e.get());
         prev_source = source;

         RETURN_IF_ERROR(source->Init(),
                         LOG(ERROR) << "Failed to initialize pipeline source");

         ASSIGN_OR_RETURN(
             image_meta,
             source->GetMetadata(required_metadata, optional_metadata),
             LOG(ERROR) << "Pipeline source " << source->name()
                        << "failed to return image metadata");

         ASSIGN_OR_RETURN(image_width, image_meta->GetOne<uint16_t>(
                                           ImageMetaId::IMAGE_WIDTH));

         ASSIGN_OR_RETURN(image_height, image_meta->GetOne<uint16_t>(
                                            ImageMetaId::IMAGE_HEIGHT));

         ASSIGN_OR_RETURN(
             samples_per_pixel,
             image_meta->GetOne<uint16_t>(ImageMetaId::SAMPLES_PER_PIXEL));
         // Pipeline source is outputting samples_per_pixel components so adjust
         // prev_components manually here.
         prev_components = samples_per_pixel;

         std::vector<std::unique_ptr<PipelineSourceChunk>> chunks;
         ASSIGN_OR_RETURN(chunks, source->GetChunks(),
                          LOG(ERROR) << "Pipeline source " << source->name()
                                     << "failed to return chunks.");

         if (chunks.size() == 1) {
            PipelineSourceChunk* chunk = chunks.at(0).get();

            size_t byte_alignment = chunk->sub_chunk_byte_alignment;
            byte_alignment = std::max(byte_alignment, sizeof(uint16_t));
            CHECK_LE(byte_alignment, chunk->chunk_data_bytes);

            // Compute processing factor (only used for processing chunks)
            uint32_t output_bytes = chunk->processed_width *
                                    chunk->processed_height * sizeof(uint16_t) *
                                    samples_per_pixel;
            double io_ratio_sqrt = 1.0;  // Default for non-processing chunks
            double proc_factor_decimal = 0.0;
            uint16_t processing_factor = 1;

            if (chunk->needs_processing) {
               CHECK_GE(output_bytes, chunk->chunk_data_bytes);
               io_ratio_sqrt = std::sqrt(static_cast<double>(output_bytes) /
                                         chunk->chunk_data_bytes);
               std::modf(io_ratio_sqrt, &proc_factor_decimal);
               processing_factor = static_cast<uint16_t>(io_ratio_sqrt);
            }

            // Determine if we can split into multiple threads
            bool can_split_for_multithreading =
                (num_threads > 1) &&
                // If sub chunk byte alignment indicates that the chunk is
                // indivisible, bail out to single thread mode.
                (byte_alignment != chunk->chunk_data_bytes) &&
                // If the bytes in each row are not exact multiples of byte
                // alignment then bail out to single thread mode as well.
                ((chunk->processed_width * sizeof(uint16_t) % byte_alignment) ==
                 static_cast<unsigned>(0)) &&
                // For processing chunks, check if processing factor is perfect
                (!chunk->needs_processing || proc_factor_decimal == 0.0);

            if (can_split_for_multithreading) {
               // Convert the chunk data to a shared_ptr and have each task
               // refer to it.
               std::shared_ptr<uint8_t[]> shared_data;
               if (chunk->owned_chunk_data) {
                  // Transfer ownership from unique_ptr to shared_ptr
                  shared_data = std::shared_ptr<uint8_t[]>(
                      chunk->owned_chunk_data.release());
               } else {
                  // For non-owned data, create a shared_ptr that doesn't delete
                  shared_data = std::shared_ptr<uint8_t[]>(
                      chunk->chunk_data_ptr, [](uint8_t*) {
                         // No-op deleter since we don't own this memory
                      });
               }
               uint32_t rows_per_task = chunk->processed_height / num_threads;
               if (rows_per_task % 2 == 1) {
                  /* Odd number of rows breaks some processors (ex. demosaic
                   * operating on CFA array).
                   * XXX: Ideally this constaint should be a processor
                   *      constraint.
                   */
                  rows_per_task++;
               }
               for (unsigned int cur_row = 0; cur_row < chunk->processed_height;
                    cur_row += rows_per_task) {
                  // The last task may have less height than rows_per_task
                  uint32_t cur_height =
                      (cur_row + rows_per_task > chunk->processed_height)
                          ? chunk->processed_height - cur_row
                          : rows_per_task;

                  // Create a sub PipelineSourceChunk referring into the
                  // original chunk
                  auto sub_chunk = std::make_unique<PipelineSourceChunk>();
                  if (chunk->needs_processing) {
                     sub_chunk->chunk_data_ptr =
                         shared_data.get() +
                         ((cur_row * chunk->processed_width *
                           samples_per_pixel) /
                          processing_factor);
                     sub_chunk->chunk_data_bytes =
                         (cur_height * chunk->processed_width *
                          samples_per_pixel) /
                         processing_factor;
                  } else {
                     // For non-processing chunks, point directly to output data
                     sub_chunk->chunk_data_ptr =
                         shared_data.get() +
                         (cur_row * chunk->processed_width * samples_per_pixel *
                          sizeof(uint16_t));
                     sub_chunk->chunk_data_bytes =
                         cur_height * chunk->processed_width *
                         samples_per_pixel * sizeof(uint16_t);
                  }
                  sub_chunk->needs_processing = chunk->needs_processing;
                  sub_chunk->sub_chunk_byte_alignment =
                      chunk->sub_chunk_byte_alignment;
                  sub_chunk->processed_height = cur_height;
                  sub_chunk->processed_width = chunk->processed_width;
                  sub_chunk->y_offset = cur_row;
                  sub_chunk->x_offset = 0;

                  std::unique_ptr<ImageBuf<uint16_t>> new_task_output;
                  std::function<absl::Status()> task_func;

                  if (chunk->needs_processing) {
                     new_task_output = std::make_unique<ImageBuf<uint16_t>>(
                         cur_height, chunk->processed_width, samples_per_pixel);
                     task_func = std::bind(&PipelineSource::ProcessChunk,
                                           source, std::cref(*sub_chunk),
                                           std::ref(*new_task_output));
                  } else {
                     new_task_output = std::make_unique<ImageBuf<uint16_t>>(
                         reinterpret_cast<uint16_t*>(sub_chunk->chunk_data_ptr),
                         /*take_ownership*/ false, cur_height,
                         chunk->processed_width, samples_per_pixel);
                     // Dummy task function that simply returns OK
                     task_func = []() -> absl::Status {
                        return absl::OkStatus();
                     };
                  }

                  auto new_task = std::make_unique<PipelineTask>(
                      cur_row, 0, task_func,
                      /*input_buf*/ nullptr,
                      /*output_buf*/ std::move(new_task_output),
                      /*source chunk*/ std::move(sub_chunk),
                      /*shared buf*/ nullptr,
                      /*shared data*/ shared_data);

                  task_runner->EnqueueRunnableTask(std::move(new_task),
                                                   cur_stage_id);
               }
            } else {  // single thread
               if (num_threads > 1) {
                  VLOG(3) << "Bailed out of trying to split a single "
                          << "input chunk into multiple threads.";
               }

               if (chunk->needs_processing) {
                  prev_output = std::make_unique<ImageBuf<uint16_t>>(
                      chunk->processed_height, chunk->processed_width,
                      samples_per_pixel);

                  RETURN_IF_ERROR(source->ProcessChunk(*chunk, *prev_output),
                                  LOG(ERROR)
                                      << "Pipeline source " << source->name()
                                      << "failed to process chunk.");
               } else {
                  // Pass ownership of chunk->chunk_data to prev_output (zero
                  // copy)
                  if (chunk->owned_chunk_data) {
                     // Transfer ownership from unique_ptr to ImageBuf
                     prev_output = std::make_unique<ImageBuf<uint16_t>>(
                         reinterpret_cast<uint16_t*>(
                             chunk->owned_chunk_data.release()),
                         /*take_ownership*/ true, chunk->processed_height,
                         chunk->processed_width, samples_per_pixel);
                  } else {
                     // For non-owned data, don't take ownership
                     prev_output = std::make_unique<ImageBuf<uint16_t>>(
                         reinterpret_cast<uint16_t*>(chunk->chunk_data_ptr),
                         /*take_ownership*/ false, chunk->processed_height,
                         chunk->processed_width, samples_per_pixel);
                  }
               }

               RETURN_IF_ERROR(source->MutateMetadata(*image_meta),
                               LOG(ERROR)
                                   << "Pipeline source " << source->name()
                                   << "failed to mutate metadata.");
               prev_source = nullptr;
            }
         } else {  // multiple chunks
            std::unique_ptr<PipelineAccumulator> accumulator = nullptr;

            for (auto& chunk : chunks) {
               std::unique_ptr<ImageBuf<uint16_t>> chunk_output;
               std::function<absl::Status()> task_func;

               if (chunk->needs_processing) {
                  chunk_output = std::make_unique<ImageBuf<uint16_t>>(
                      chunk->processed_height, chunk->processed_width,
                      samples_per_pixel);
                  task_func =
                      std::bind(&PipelineSource::ProcessChunk, source,
                                std::cref(*chunk), std::ref(*chunk_output));
               } else {  // processing not needed
                  if (chunk->owned_chunk_data) {
                     // Transfer ownership from unique_ptr to ImageBuf
                     chunk_output = std::make_unique<ImageBuf<uint16_t>>(
                         reinterpret_cast<uint16_t*>(
                             chunk->owned_chunk_data.release()),
                         /*take_ownership*/ true, chunk->processed_height,
                         chunk->processed_width, samples_per_pixel);
                  } else {
                     // For non-owned data, don't take ownership
                     chunk_output = std::make_unique<ImageBuf<uint16_t>>(
                         reinterpret_cast<uint16_t*>(chunk->chunk_data_ptr),
                         /*take_ownership*/ false, chunk->processed_height,
                         chunk->processed_width, samples_per_pixel);
                  }
                  // dummy task function that simply returns OK
                  task_func = []() -> absl::Status { return absl::OkStatus(); };
               }

               if (num_threads > 1) {
                  auto task = std::make_unique<PipelineTask>(
                      chunk->y_offset, chunk->x_offset, task_func,
                      /*input_buf*/ nullptr,
                      /*output_buf*/ std::move(chunk_output),
                      /*source_chunk*/ std::move(chunk));

                  task_runner->EnqueueRunnableTask(std::move(task),
                                                   cur_stage_id);
               } else {
                  if (accumulator == nullptr) {  // alloc if first chunk
                     accumulator = std::make_unique<PipelineAccumulator>(
                         image_height, image_width, samples_per_pixel);
                  }

                  RETURN_IF_ERROR(task_func(),
                                  LOG(ERROR)
                                      << "Pipeline source " << source->name()
                                      << "failed to process chunk.");

                  RETURN_IF_ERROR(accumulator->Accumulate(
                      chunk->y_offset, chunk->x_offset, *chunk_output));
               }
            }

            if (accumulator) {
               prev_output = accumulator->ReleaseBuffer();
            }

            if (num_threads == 1) {
               RETURN_IF_ERROR(source->MutateMetadata(*image_meta),
                               LOG(ERROR)
                                   << "Pipeline source " << source->name()
                                   << "failed to mutate metadata.");
               prev_source = nullptr;
            }
         }

         // Mark the source stage as complete and move to the next stage
         if (task_runner) {
            task_runner->MarkStageAsComplete(cur_stage_id);
            cur_stage_id++;
         }
      } else if (e->type() == PipelineElementType::PROCESSOR) {
         PipelineProcessor* processor =
             static_cast<PipelineProcessor*>(e.get());

         // XXX: this should be fixed for processors that can do either/or based
         //  on what the previous processor output.
         size_t components;
         bool one_spp = processor->output_spec().one_sample_per_pixel;
         bool three_spp = processor->output_spec().three_samples_per_pixel;
         if (one_spp && three_spp) {
            // Processor can do either mode, choose based on previous element's
            // output. Set components to zero and have the task code below
            // populate it.
            components = 0;
         } else if (one_spp) {
            components = 1;
         } else {
            components = 3;
         }

         if (task_runner &&
             task_runner->StageHasPendingTasks(cur_stage_id - 1)) {
            // Run metadata updates before running Init() or enqueueing any
            // tasks for the current stage since the current stage's tasks can
            // start running right away and need to see updates from the
            // previous stage.
            if (prev_processor != nullptr) {
               prev_processor->LogStats();
               RETURN_IF_ERROR(prev_processor->MutateMetadata(*image_meta),
                               LOG(ERROR) << "Task failed to update metadata "
                                          << prev_processor->name());
            }
            if (prev_source != nullptr) {
               RETURN_IF_ERROR(prev_source->MutateMetadata(*image_meta),
                               LOG(ERROR)
                                   << "Pipeline source " << prev_source->name()
                                   << "failed to mutate metadata.");
               prev_source = nullptr;
            }

            // Initialize the processor before processing tasks
            PrintElementInfo(processor);
            RETURN_IF_ERROR(processor->Init(*image_meta),
                            LOG(ERROR)
                                << "Failed to initialize pipeline processor: "
                                << processor->name());
            processor->LogStart();

            // Process all tasks from the previous stage
            // Keep dequeuing tasks until there are no more pending tasks in the
            // previous stage
            while (task_runner->StageHasPendingTasks(cur_stage_id - 1)) {
               auto&& [status, completed_task] =
                   task_runner->DequeueCompletedTask(cur_stage_id - 1);
               if (!status.ok()) {
                  LOG(ERROR) << "Task failed for " << prev_element->name()
                             << " with: " << status;
                  return status;
               }

               // Hand over the previous task's output as input to the new task
               std::unique_ptr<ImageBuf<uint16_t>> new_task_input(
                   completed_task->ReleaseOutputBuf());
               // Use the previous task's component count for the new task
               // if this processor can do both 1pp and 3pp
               if (components == 0) {
                  components = new_task_input->get_components();
               }

               // If any sink accumulators are present, accumulate the previous
               // task's output
               for (auto& accumulator : accumulators) {
                  RETURN_IF_ERROR(accumulator->Accumulate(
                      completed_task->y_offset(), completed_task->x_offset(),
                      *new_task_input));
               }

               auto new_task_output = std::make_unique<ImageBuf<uint16_t>>(
                   new_task_input->get_height(), new_task_input->get_width(),
                   components);

               // Create task function that captures raw pointers to the buffers
               ImageBuf<uint16_t>* input_ptr = new_task_input.get();
               ImageBuf<uint16_t>* output_ptr = new_task_output.get();
               auto task_func = [processor, input_ptr,
                                 output_ptr]() -> absl::Status {
                  return processor->Run(*input_ptr, *output_ptr);
               };

               auto new_task = std::make_unique<PipelineTask>(
                   completed_task->y_offset(), completed_task->x_offset(),
                   task_func,
                   /*input_buf*/ std::move(new_task_input),
                   /*output_buf*/ std::move(new_task_output));

               // Enqueue the task directly into the current stage
               task_runner->EnqueueRunnableTask(std::move(new_task),
                                                cur_stage_id);
            }

            accumulators.clear();

            prev_components = components;
         } else {  // single ImageBuf from previous stage
            // Use the previous element's component count for the current
            // processor if the current processor can do both 1pp and 3pp
            if (components == 0) {
               components = prev_output->get_components();
            }
            PrintElementInfo(processor);
            RETURN_IF_ERROR(processor->Init(*image_meta),
                            LOG(ERROR)
                                << "Failed to initialize pipeline processor: "
                                << processor->name());
            processor->LogStart();

            if (num_threads > 1) {
               /*
                * Since we are running with multiple threads enabled we want to
                * break apart the ImageBuf from the previous stage into
                * num_threads tasks. We convert the prev_output buffer to a
                * shared_ptr and have each task refer to it.
                */
               std::shared_ptr<ImageBuf<uint16_t>> shared_buf(
                   prev_output.release());

               uint32_t rows_per_task = shared_buf->get_height() / num_threads;
               if (rows_per_task % 2 == 1) {
                  // Odd number of rows breaks some processors (ex. demosaic
                  // operating on CFA array). Ideally this constaint should
                  // be a processor flag.
                  rows_per_task++;
               }

               for (unsigned int cur_row = 0;
                    cur_row < shared_buf->get_height();
                    cur_row += rows_per_task) {
                  // The last task may have less height than rows_per_task
                  uint32_t cur_height =
                      (cur_row + rows_per_task > shared_buf->get_height())
                          ? shared_buf->get_height() - cur_row
                          : rows_per_task;

                  // Create a shallow ImageBuf referring into shared_buf
                  auto new_task_input = std::make_unique<ImageBuf<uint16_t>>(
                      shared_buf->get_ptr(cur_row, 0),
                      /*take_ownership*/ false, cur_height,
                      shared_buf->get_width(), shared_buf->get_components());

                  auto new_task_output = std::make_unique<ImageBuf<uint16_t>>(
                      cur_height, shared_buf->get_width(), components);

                  // Create task function that captures raw pointers to the
                  // buffers
                  ImageBuf<uint16_t>* input_ptr = new_task_input.get();
                  ImageBuf<uint16_t>* output_ptr = new_task_output.get();
                  auto task_func = [processor, input_ptr,
                                    output_ptr]() -> absl::Status {
                     return processor->Run(*input_ptr, *output_ptr);
                  };

                  auto new_task = std::make_unique<PipelineTask>(
                      cur_row, 0, task_func,
                      /*input_buf*/ std::move(new_task_input),
                      /*output_buf*/ std::move(new_task_output),
                      /*source chunk*/ nullptr,
                      /*shared buf*/ shared_buf);

                  task_runner->EnqueueRunnableTask(std::move(new_task),
                                                   cur_stage_id);
               }
            } else {  // single thread
               auto processor_output = std::make_unique<ImageBuf<uint16_t>>(
                   image_height, image_width, components);
               RETURN_IF_ERROR(processor->Run(*prev_output, *processor_output),
                               LOG(ERROR)
                                   << "Failed to run pipeline processor: "
                                   << processor->name());
               processor->LogStats();
               RETURN_IF_ERROR(
                   processor->MutateMetadata(*image_meta),
                   LOG(ERROR)
                       << "Pipeline processor failed to update metadata: "
                       << processor->name());

               prev_output = std::move(processor_output);
            }
         }

         // Mark the current stage as complete and move to the next stage
         if (task_runner) {
            task_runner->MarkStageAsComplete(cur_stage_id);
            cur_stage_id++;
         }

         prev_processor = processor;
      } else if (e->type() == PipelineElementType::SINK) {
         PipelineSink* sink = static_cast<PipelineSink*>(e.get());

         if (task_runner &&
             task_runner->StageHasPendingTasks(cur_stage_id - 1)) {
            if (e.get() == elements_.back().get()) {
               // If this is the last pipeline element we need to drain the
               // pipeline directly
               PipelineAccumulator accumulator(image_height, image_width,
                                               prev_components);

               // Process all tasks from the previous stage
               while (task_runner->StageHasPendingTasks(cur_stage_id - 1)) {
                  auto&& [status, completed_task] =
                      task_runner->DequeueCompletedTask(cur_stage_id - 1);
                  if (!status.ok()) {
                     LOG(ERROR) << "Task failed for " << prev_element->name()
                                << " with: " << status;
                     return status;
                  }

                  RETURN_IF_ERROR(accumulator.Accumulate(
                      completed_task->y_offset(), completed_task->x_offset(),
                      *completed_task->output_buf()));
               }
               CHECK(accumulator.IsComplete());

               // Make sure metadata is updated after all tasks are processed
               if (prev_processor != nullptr) {
                  prev_processor->LogStats();
                  RETURN_IF_ERROR(prev_processor->MutateMetadata(*image_meta),
                                  LOG(ERROR)
                                      << "Task failed to update metadata "
                                      << prev_processor->name());
               }
               if (prev_source != nullptr) {
                  RETURN_IF_ERROR(prev_source->MutateMetadata(*image_meta),
                                  LOG(ERROR) << "Pipeline source "
                                             << prev_source->name()
                                             << "failed to mutate metadata.");
                  prev_source = nullptr;
               }

               PrintElementInfo(e.get());

               auto collapsed_output = accumulator.ReleaseBuffer();
               RETURN_IF_ERROR(
                   sink->OutputImage(*collapsed_output, *image_meta),
                   LOG(ERROR) << "Failed to output image to pipeline sink: "
                              << sink->name());
            } else {  // not the last pipeline element
               // Create an accumulator to gather task output and move to next
               // pipeline stage
               auto accumulator = std::make_unique<PipelineAccumulator>(
                   image_height, image_width, prev_components);
               accumulator->RegisterCompletionFunc(
                   std::bind(&PipelineSink::OutputImage, sink,
                             std::placeholders::_1, std::cref(*image_meta)));
               accumulators.push_back(std::move(accumulator));
            }
         } else {  // single ImageBuf from previous element
            PrintElementInfo(e.get());
            RETURN_IF_ERROR(sink->OutputImage(*prev_output, *image_meta),
                            LOG(ERROR)
                                << "Failed to output image to pipeline sink: "
                                << sink->name());
         }
      }

      prev_element = e.get();
   }

   return absl::OkStatus();
}

}  // namespace cmrw
